# Enhanced MCP HTTP Server Guide

This guide shows how to use the enhanced MCP HTTP server that provides both local tools AND delegates to third-party MCP servers, all through the same port 9000 that `chat_term` already connects to.

## Architecture

```
chat_term -> Enhanced MCP Server (Port 9000) -> Local Tools + Third-party MCP Servers
```

**Benefits:**
- No changes to `chat_term` connection
- Same port 9000 as before
- Local tools + third-party tools in one place
- Namespaced third-party tools (e.g., `web__firecrawl_scrape`)
- Configuration-driven third-party setup
- Parameter mapping and default parameters for seamless integration
- Process spawning and lifecycle management for backend servers

## Quick Setup

### 1. Create Configuration (Optional)

```bash
# Create example configuration for third-party servers
python mcp_http_server_enhanced.py --create-config
```

This creates `server_config.json` using the Augment pattern:

```json
{
  "description": "Enhanced MCP Server Configuration (Augment Pattern)",
  "mcpServers": {
    "firecrawl-mcp": {
      "enabled": false,
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
      },
      "namespace": "web",
      "description": "Firecrawl MCP server spawned via npx",
      "parameterMapping": {
        "firecrawl_scrape": {},
        "firecrawl_search": {
          "q": "query"
        }
      },
      "defaultParameters": {
        "firecrawl_scrape": {
          "formats": ["markdown"],
          "onlyMainContent": true
        }
      }
    },
    "brave-search-mcp": {
      "enabled": false,
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_SEARCH_API_KEY": "{BRAVE_SEARCH_API_KEY}"
      },
      "namespace": "search",
      "description": "Brave Search API for web and local search",
      "parameterMapping": {
        "brave_web_search": {
          "q": "query"
        }
      },
      "defaultParameters": {
        "brave_web_search": {
          "count": 10
        }
      }
    }
  }
}
```

### 2. Start Enhanced Server

```bash
# Start with default configuration (local tools only)
python mcp_http_server_enhanced.py --port 9000

# Start with third-party server configuration
python mcp_http_server_enhanced.py --port 9000 --config server_config.json
```

### 3. Connect chat_term (Same as Before!)

```bash
# Same command as always - no changes needed!
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://localhost:9000/mcp
```

## Available Tools

### Local Tools (Always Available)
- `echostring` - Echo a string back
- `echostring_table` - Create a table from items
- `long_task` - Long-running task with progress
- `firecrawl_scrape_text_only` - Local web scraping
- `server_status` - Get server and third-party connection status
- `list_all_tools` - List all available tools (local + third-party)
- `third_party_health` - Check health of third-party connections

### Third-party Tools (When Configured)
- `web__firecrawl_scrape` - Firecrawl web scraping (if Firecrawl server enabled)
- `web__firecrawl_search` - Firecrawl web search (if Firecrawl server enabled)
- `search__brave_web_search` - Brave web search (if Brave server enabled)
- `search__brave_local_search` - Brave local search (if Brave server enabled)
- `exa__search` - Exa embedding-based search (if Exa server enabled)

## Configuration Options

### Enable Firecrawl Integration

1. Set environment variable:
```bash
export FIRECRAWL_API_KEY="your_api_key_here"
```

2. Edit `server_config.json`:
```json
{
  "mcpServers": {
    "firecrawl-mcp": {
      "enabled": true,
      "command": "npx",
      "args": ["-y", "firecrawl-mcp"],
      "env": {
        "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
      },
      "namespace": "web",
      "parameterMapping": {
        "firecrawl_search": {
          "q": "query"
        }
      },
      "defaultParameters": {
        "firecrawl_scrape": {
          "formats": ["markdown"],
          "onlyMainContent": true
        }
      }
    }
  }
}
```

3. Restart the server:
```bash
python mcp_http_server_enhanced.py --port 9000 --config server_config.json
```

### Add Custom Third-party Server

```json
{
  "mcpServers": {
    "my-custom-server": {
      "enabled": true,
      "command": "python",
      "args": ["my_custom_mcp_server.py", "--port", "8080"],
      "env": {
        "API_KEY": "{MY_API_KEY}"
      },
      "namespace": "custom",
      "description": "My custom MCP server",
      "parameterMapping": {
        "my_tool": {
          "old_param": "new_param"
        }
      },
      "defaultParameters": {
        "my_tool": {
          "timeout": 30
        }
      }
    }
  }
}
```

## Usage Examples in chat_term

### Local Tools
```
> Use echostring to echo "Hello from local server"
> Create a table with echostring_table using items ["A", "B", "C"]
> Run long_task for 10 seconds to test progress reporting
> Get server_status to see all connections
```

### Third-party Tools (if configured)
```
> Use web__firecrawl_scrape to scrape https://example.com
> Use search__brave_web_search to search for "AI news"
> Use exa__search to find embedding-based results for "machine learning"
> Check third_party_health to verify all connections
> List all available tools with list_all_tools
```

### Server Management
```
> Get server_status to see local and third-party server info
> Check third_party_health to verify third-party connections
> Use list_all_tools to see everything available
```

## Comparison with Original Server

### Original `mcp_http_server.py`
- Only local tools
- Simple, lightweight
- No third-party integration

### Enhanced `mcp_http_server_enhanced.py`
- Local tools + third-party delegation
- Namespaced third-party tools (using double underscores: `__`)
- Configuration-driven with Augment pattern
- Process spawning and lifecycle management
- Parameter mapping and default parameters
- Server management tools
- Same port and interface

## Migration from Original Server

### No Changes Needed for chat_term
The enhanced server is a drop-in replacement:

```bash
# Before (original server)
python mcp_http_server.py --port 9000

# After (enhanced server)
python mcp_http_server_enhanced.py --port 9000
```

`chat_term` connects the same way and gets all the original tools plus any configured third-party tools.

### Gradual Third-party Addition
1. Start with enhanced server (no config) - same as original
2. Add configuration file when ready for third-party tools
3. Enable third-party servers one by one
4. No disruption to existing workflows

## Troubleshooting

### Enhanced Server Won't Start
1. Check if port 9000 is available
2. Verify configuration file syntax (must use `mcpServers` key)
3. Check environment variables for third-party servers
4. Verify `command` and `args` are correct for each server
5. Review server logs for specific errors

### Third-party Tools Not Available
1. Use `server_status` to check third-party connections
2. Use `third_party_health` to test connectivity
3. Verify backend server processes are running
4. Check parameter mappings and default parameters
5. Verify environment variable substitution is working
6. Check backend server logs for errors

### Parameter Mapping Issues
1. Verify parameter mapping syntax in configuration
2. Check that mapped parameters exist in tool schema
3. Use debug logging to see parameter transformations
4. Test with simple parameter mappings first

### Process Spawning Issues
1. Verify `command` exists and is executable
2. Check `args` array syntax and values
3. Verify environment variables are properly set
4. Check process permissions and PATH
5. Test command manually before adding to config

### Performance Issues
1. Use `third_party_health` to identify slow connections
2. Check backend server process health
3. Consider disabling problematic third-party servers
4. Monitor server logs for timeout errors
5. Check async context management for proper cleanup

## Advanced Configuration

### Parameter Mapping

The enhanced server supports parameter mapping to adapt tool calls between client expectations and backend server requirements:

```json
{
  "mcpServers": {
    "brave-search-mcp": {
      "parameterMapping": {
        "brave_web_search": {
          "q": "query"  // Maps 'q' parameter to 'query'
        },
        "brave_local_search": {
          "q": "query"
        }
      }
    }
  }
}
```

This allows the client to call `search__brave_web_search(q="search term")` while the backend receives `brave_web_search(query="search term")`.

### Default Parameters

You can specify default parameters that are automatically added to tool calls:

```json
{
  "mcpServers": {
    "firecrawl-mcp": {
      "defaultParameters": {
        "firecrawl_scrape": {
          "formats": ["markdown"],
          "onlyMainContent": true,
          "timeout": 30
        }
      }
    }
  }
}
```

Default parameters are only applied if the parameter is not already provided in the tool call.

## Conclusion

This enhanced server approach gives you the best of both worlds: all your existing functionality continues to work exactly the same, but you can gradually add third-party MCP server integration as needed, all through the same familiar interface that `chat_term` already uses!
