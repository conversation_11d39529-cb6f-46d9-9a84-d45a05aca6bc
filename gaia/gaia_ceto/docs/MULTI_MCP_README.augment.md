# Multi-MCP Enhanced Server Architecture

## Overview

The Multi-MCP Enhanced Server system provides a unified interface for accessing tools from multiple MCP (Model Context Protocol) servers through a single connection point. This architecture enables chat applications to leverage specialized tools from various providers while maintaining a simple, consistent interface.

## Architecture Components

### 1. Enhanced MCP Server (`mcp_http_server_enhanced.py`)
**Role**: Central hub and orchestrator
- **Port**: 9000 (HTTP streaming)
- **Purpose**: Aggregates local tools and delegates to third-party MCP servers
- **Key Features**:
  - Local tool hosting (echostring, long_task, server management)
  - Third-party server delegation with namespacing
  - Config-driven parameter mapping and default values
  - Connection management and health monitoring
  - Automatic reconnection and error handling

### 2. Multi-MCP Client (`multi_mcp_client.py`)
**Role**: Connection manager and tool router
- **Purpose**: Manages connections to multiple backend MCP servers
- **Protocols Supported**: HTTP, SSE, stdio
- **Key Features**:
  - Dynamic server spawning (stdio-based servers)
  - Connection pooling and lifecycle management
  - Tool discovery and aggregation
  - Auto-routing and load balancing
  - Graceful error handling and reconnection

### 3. Chat Terminal (`chat_term.py`)
**Role**: User interface and LLM integration
- **Purpose**: Provides chat interface with MCP tool access
- **Connection**: Single connection to Enhanced MCP Server (port 9000)
- **Key Features**:
  - Multiple LLM provider support (Anthropic, OpenAI, Mock)
  - Transparent tool access across all backend servers
  - Conversation management and persistence
  - Progress reporting for long-running tasks

## Execution Flow

### 1. System Initialization

```
1. Enhanced MCP Server starts on port 9000
2. Loads server_config.json configuration
3. Validates parameter mappings and default values
4. Spawns configured third-party MCP servers:
   - Firecrawl MCP (web scraping)
   - Brave Search MCP (web search)
   - Exa MCP (semantic search)
5. Registers local tools and delegated tools with namespacing
6. Starts HTTP streaming server for client connections
```

### 2. Client Connection Flow

```
1. chat_term connects to http://localhost:9000/mcp
2. Enhanced MCP Server provides aggregated tool list:
   - Local tools: echostring, long_task, server_status
   - Delegated tools: search__brave_web_search, fc__firecrawl_scrape
3. chat_term receives unified tool interface
4. LLM can now use any tool transparently
```

### 3. Tool Call Execution Flow

```
User Input: "Search for recent AI developments"
    ↓
chat_term → LLM decides to use search__brave_web_search
    ↓
HTTP POST to Enhanced MCP Server (/mcp endpoint)
    ↓
Enhanced MCP Server:
  1. Parses tool name: search__brave_web_search
  2. Identifies server: brave-search-mcp
  3. Maps tool name: brave_web_search
  4. Applies parameter mapping: q → query
  5. Applies default parameters: count = 10
    ↓
Multi-MCP Client routes to brave-search-mcp (stdio)
    ↓
Brave Search MCP executes search
    ↓
Results flow back through the chain:
  brave-search-mcp → Multi-MCP Client → Enhanced MCP Server → chat_term
    ↓
LLM processes results and responds to user
```

## Configuration System

### Server Configuration (`server_config.json`)

The system uses a declarative configuration approach:

```json
{
  "mcpServers": {
    "brave-search-mcp": {
      "enabled": true,
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-brave-search"],
      "env": {
        "BRAVE_SEARCH_API_KEY": "{BRAVE_SEARCH_API_KEY}"
      },
      "namespace": "search",
      "description": "Brave Search API for web and local search",
      "parameterMapping": {
        "brave_web_search": {
          "q": "query"
        }
      },
      "defaultParameters": {
        "brave_web_search": {
          "count": 10
        }
      }
    }
  }
}
```

### Key Configuration Features

1. **Environment Variable Substitution**: `{BRAVE_SEARCH_API_KEY}` → actual API key
2. **Parameter Mapping**: Translates client parameters to server expectations
3. **Default Parameters**: Automatically applies common defaults
4. **Namespacing**: Prevents tool name conflicts across servers
5. **Enable/Disable**: Easy server management without code changes

## Parameter Mapping System

### Problem Solved
Different MCP servers expect different parameter names for similar functionality:
- Chat interfaces commonly use `q` for search queries
- Brave Search MCP expects `query`
- Firecrawl Search expects `query`

### Solution: Config-Driven Mapping

```python
# Before mapping (from chat_term/LLM):
{"q": "artificial intelligence", "count": 10}

# After mapping (to Brave Search MCP):
{"query": "artificial intelligence", "count": 10}
```

### Implementation
1. **Configuration Loading**: Parameter mappings loaded at server startup
2. **Runtime Application**: Applied during tool call processing
3. **Validation**: Configuration validated for correctness
4. **Logging**: All mappings logged for debugging

## Connection Management

### Protocol Support

1. **HTTP**: Traditional request/response with streaming
2. **SSE (Server-Sent Events)**: Real-time streaming connections
3. **stdio**: Process-based MCP servers (most third-party servers)

### Connection Lifecycle

```
1. Server Spawning (stdio servers):
   - Process creation with environment variables
   - MCP handshake and capability negotiation
   - Tool discovery and registration

2. Connection Monitoring:
   - Health checks and heartbeat monitoring
   - Automatic reconnection on failures
   - Graceful degradation when servers unavailable

3. Cleanup:
   - Proper process termination
   - Resource cleanup and connection closure
   - Error logging and reporting
```

### Error Handling

1. **Connection Errors**: Automatic reconnection with exponential backoff
2. **Tool Call Errors**: Detailed error reporting with context
3. **Server Failures**: Graceful degradation, other servers continue working
4. **Parameter Errors**: Clear validation messages and suggestions

## Tool Namespacing

### Purpose
Prevents conflicts when multiple servers provide similar tools.

### Naming Convention
```
{namespace}__{tool_name}
```

### Examples
- `search__brave_web_search` (Brave Search)
- `fc__firecrawl_scrape` (Firecrawl)
- `exa__exa_search` (Exa)

### Benefits
1. **Conflict Prevention**: No tool name collisions
2. **Clear Attribution**: Easy to identify tool source
3. **Organized Interface**: Logical grouping of related tools
4. **Scalability**: Easy to add new servers without conflicts

## Development and Debugging

### Logging Levels
- **INFO**: Connection status, tool registrations, parameter mappings
- **DEBUG**: Detailed execution flow, parameter transformations
- **ERROR**: Connection failures, tool call errors, configuration issues
- **WARNING**: Reconnection attempts, degraded functionality

### Debug Features
1. **Server Status Tool**: Real-time health monitoring
2. **Tool Listing**: Complete inventory of available tools
3. **Connection Health**: Individual server status checks
4. **Parameter Tracing**: Detailed parameter transformation logs

### Testing Tools
- **Mock LLM**: Safe testing without API costs
- **Health Endpoints**: Programmatic status checking
- **Configuration Validation**: Startup-time config verification

## Usage Examples

### Starting the System
```bash
# 1. Start Enhanced MCP Server
cd gaia/gaia_ceto/proto_mcp_http
python mcp_http_server_enhanced.py --port 9000 --config server_config.json

# 2. Connect chat_term
python -m gaia.gaia_ceto.ceto_v002.chat_term \
  --llm mcp-http \
  --mcp-http-server http://localhost:9000/mcp
```

### Tool Usage in Chat
```
> Search for recent developments in artificial intelligence
[Uses search__brave_web_search automatically]

> Scrape the content from https://example.com
[Uses fc__firecrawl_scrape if Firecrawl is enabled]

> Check the status of all connected servers
[Uses server_status local tool]
```

## Benefits of This Architecture

1. **Unified Interface**: Single connection point for multiple specialized tools
2. **Scalability**: Easy addition of new MCP servers without client changes
3. **Flexibility**: Config-driven parameter mapping and server management
4. **Reliability**: Robust error handling and automatic reconnection
5. **Maintainability**: Clear separation of concerns and modular design
6. **Developer Experience**: Rich debugging and monitoring capabilities

## Future Enhancements

1. **Load Balancing**: Distribute calls across multiple instances of same tool
2. **Caching**: Cache frequently used tool results
3. **Rate Limiting**: Protect backend servers from overload
4. **Authentication**: Secure access control for different tool sets
5. **Metrics**: Detailed performance and usage analytics
