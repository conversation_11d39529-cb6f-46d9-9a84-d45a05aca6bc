# Multi-MCP Execution Model

This document describes the architecture and execution model of the enhanced, multi-MCP (Model Context Protocol) system. This system is designed to be a robust, flexible, and scalable way to provide tools to a language model client like `chat_term`.

## 1. Core Architecture: The Delegator/Hub-and-Spoke Model

The system is not a single, monolithic server. Instead, it follows a **Delegator** (or **Hub-and-Spoke**) pattern. This architecture consists of three primary components:

1.  **The Client (`chat_term`)**: The user-facing application that communicates with a language model.
2.  **The Enhanced MCP Server (The "Hub")**: A central, delegating server that manages and aggregates tools from other servers. This is implemented in `mcp_http_server_enhanced.py`.
3.  **Backend MCP Servers (The "Spokes")**: Multiple, independent, and specialized servers that each provide a specific set of tools (e.g., for web search, file system access, etc.).

The key principle is **decoupling**. The `chat_term` client is completely unaware of the complexity of the backend. It only ever connects to the single Enhanced MCP Server hub.

### Visualization

```
+-----------+               +--------------------------+         +--------------------------+
|           |               |                          |         |                          |
| chat_term |<------------->| mcp_http_server_enhanced |<------->| Backend Server 1 (e.g.,  |
| (Client)  |   (HTTP/SSE)  | (The Hub / Delegator)    |         | Brave Search via Python) |
|           |               |                          |         +--------------------------+
+-----------+               +--------------------------+
                                      ^
                                      |
                                      |                +--------------------------+
                                      +--------------->|                          |
                                                       | Backend Server 2 (e.g.,  |
                                                       | Firecrawl via npx)       |
                                                       |                          |
                                                       +--------------------------+
```

---

## 2. Component Roles

### a) The Client (`chat_term`)

-   **Role**: User Interface / LLM Interaction.
-   **Responsibilities**:
    -   Connect to a *single* MCP endpoint: the URL of the Enhanced MCP Server.
    -   Request the list of available tools from that single endpoint.
    -   Send tool call requests to that endpoint.
    -   Receive and display the results streamed from that endpoint.
-   **Key Characteristic**: It has no knowledge of the backend servers. It sees a single, unified list of tools.

### b) The Enhanced MCP Server (`mcp_http_server_enhanced.py`)

-   **Role**: Central Hub, Aggregator, and Delegator.
-   **Responsibilities**:
    -   **Configuration Management**: On startup, it reads `server_config.json` to discover which backend servers to manage.
    -   **Process Spawning**: It dynamically spawns the backend servers as child processes (e.g., by running `python brave_search_mcp.py` or `npx ...`). It also manages their lifecycle.
    -   **Tool Aggregation**: It connects to each backend server, retrieves its list of tools, and aggregates them into a single master list.
    -   **Namespacing**: To prevent name collisions between tools from different servers, it prepends a `namespace` (defined in the config) to each tool name. For example, the `search` tool from the `brave` server becomes `brave__search`.
    -   **Parameter Mapping**: It can adapt the parameters passed by the client to match what the backend tool expects. This logic is defined either in the server's code or declaratively in `server_config.json`. This is crucial for maintaining a stable client-facing API.
    -   **Delegation**: When it receives a tool call for a namespaced tool (e.g., `brave__search`), it identifies the correct backend server and forwards (delegates) the request to it.
    -   **Result Proxying**: It receives the result from the backend server and streams it back to the `chat_term` client.

### c) Backend MCP Servers (The "Spokes")

-   **Role**: Specialized Tool Provider.
-   **Responsibilities**:
    -   Implement a specific, well-defined set of tools (e.g., `search` for Brave, `scrape` for Firecrawl).
    -   Expose these tools via the Model Context Protocol.
    -   Execute the tool logic when called and return a result.
-   **Key Characteristic**: They are completely self-contained. A backend server has no knowledge of the Enhanced Server or any other backend servers. It can be a Python script, a Node.js application, or any other process that speaks MCP.

---

## 3. Step-by-Step Execution Flow

This is the sequence of events for a typical session:

1.  **Start the Hub**: The user runs `python mcp_http_server_enhanced.py`.
2.  **Hub Reads Config**: The Enhanced Server parses `server_config.json`.
3.  **Hub Spawns Spokes**: For each `enabled` server in the configuration, the Hub executes its `command` and `args` as a new child process, passing the specified `env` variables.
    -   *Example*: It runs `python brave_search_mcp.py --port 9001` as one process.
    -   *Example*: It runs `npx -y firecrawl-mcp` as another process.
4.  **Hub Connects and Registers Tools**: The Hub connects to each of its child processes, fetches their tool schemas, and creates the namespaced, delegated tool functions internally.
5.  **Start the Client**: The user runs `chat_term`, pointing it to the Hub's address (e.g., `http://localhost:9000/mcp`).
6.  **Client Requests Tools**: `chat_term` asks the Hub for the list of available tools. The Hub returns the fully aggregated and namespaced list (e.g., `local__echostring`, `brave__search`, `web__firecrawl_scrape`).
7.  **User/LLM Issues a Tool Call**: The LLM decides to call `brave__search(query="What is Gemini?")`.
8.  **Client Sends Request**: `chat_term` sends this tool call to the Hub.
9.  **Hub Delegates the Call**:
    -   The Hub inspects the tool name `brave__search`.
    -   It recognizes the `brave__` namespace and knows this call belongs to the `brave-search-py` backend server.
    -   It performs any configured parameter mapping.
    -   It forwards the `search` tool call with the mapped parameters to the `brave_search_mcp.py` process.
10. **Spoke Executes the Tool**: The `brave_search_mcp.py` server receives the request, calls the Brave Search API, formats the result, and returns it to the Hub.
11. **Hub Proxies the Result**: The Hub receives the result from the backend server.
12. **Client Receives the Result**: The Hub streams the final result back to `chat_term`, which displays it to the user.

This model provides a powerful separation of concerns, allowing new tools and capabilities to be added to the system simply by creating a new backend server and adding an entry to the `server_config.json` file, without ever needing to modify the client application.
