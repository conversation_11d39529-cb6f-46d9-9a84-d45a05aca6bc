#!/usr/bin/env python3
"""
Test script to verify timeout configuration is working correctly.
"""

import json
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from gaia.gaia_ceto.proto_mcp_http.mcp_http_server_multi import MCPServerMulti


def test_timeout_configuration():
    """Test that timeout configuration is loaded and applied correctly."""
    
    # Create a test server instance
    server = MCPServerMulti("server_config.json")
    
    # Load configuration
    import asyncio
    async def test():
        await server.load_configuration()
        
        # Test global settings
        print("Global Settings:")
        print(f"  Default timeout: {server.global_settings.get('defaultTimeout')}s")
        print(f"  Long-running timeout: {server.global_settings.get('longRunningToolTimeout')}s")
        print()
        
        # Test tool-specific timeouts
        test_cases = [
            ("firecrawl-hosted", "firecrawl_scrape"),
            ("firecrawl-hosted", "firecrawl_crawl"),
            ("firecrawl-hosted", "firecrawl_search"),
            ("firecrawl-hosted", "unknown_tool"),
            ("unknown-server", "unknown_tool"),
        ]
        
        print("Tool Timeout Tests:")
        for server_id, tool_name in test_cases:
            timeout = server.get_tool_timeout(server_id, tool_name)
            print(f"  {server_id}/{tool_name}: {timeout}s")
        
        print()
        print("✅ Timeout configuration test completed successfully!")
    
    asyncio.run(test())


if __name__ == "__main__":
    test_timeout_configuration()
