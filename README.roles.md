# MCP System File Roles

This document provides a concise overview of the key files in the MCP (Model Context Protocol) system and their specific roles.

## Core Files

### **chat_term.py**
- **Terminal-based chat interface** that provides a REPL-style command-line interface for interacting with the chat system. Supports multiple LLM providers (Mock, OpenAI, Anthropic, MCP SSE, MCP HTTP) with conversation management and direct tool calling capabilities.

### **multi_mcp_client_clean.py**
- **Clean multi-server MCP client** for connecting to multiple MCP servers simultaneously across different protocols (HTTP, SSE, stdio). Provides unified interface for calling tools across different servers with robust connection management and auto-discovery.

### **mcp_http_server_multi.py**
- **Production-ready aggregate MCP server** that combines local FastMCP tools with third-party MCP server delegation. Uses configuration-driven setup to host local tools while proxying calls to external MCP servers with parameter mapping support.

### **mcp_http_clientlib.py**
- **Reusable HTTP client library** for connecting to MCP servers via streamable HTTP transport. Handles core MCP functionality including server connection, tool calling, and Claude integration with comprehensive error handling.

### **mcp_sse_clientlib.py**
- **Reusable SSE client library** for connecting to MCP servers via Server-Sent Events transport. Provides identical functionality to the HTTP client but uses SSE protocol for real-time communication with MCP servers.

### **server_config.json**
- **Configuration file** that defines third-party MCP server connections for the aggregate server, including Firecrawl, Exa, and Brave Search servers. Specifies connection details (URLs/commands), parameter mappings, default parameters, and namespacing for tool organization.

## System Architecture

The files work together to create a comprehensive MCP ecosystem:

- **chat_term.py** serves as the user interface
- **mcp_http_clientlib.py** and **mcp_sse_clientlib.py** provide protocol-specific connectivity
- **multi_mcp_client_clean.py** manages multiple server connections
- **mcp_http_server_multi.py** acts as an aggregating server that combines local and remote MCP capabilities
- **server_config.json** drives the configuration for third-party server integrations

## File Locations

```
/home/<USER>/django-projects/agbase_admin/
├── gaia/gaia_ceto/ceto_v002/chat_term.py
├── gaia/gaia_ceto/proto_mcp_http/multi_mcp_client_clean.py
├── gaia/gaia_ceto/proto_mcp_http/mcp_http_server_multi.py
├── gaia/gaia_ceto/proto_mcp_http/mcp_http_clientlib.py
├── gaia/gaia_ceto/proto_mcp/mcp_sse_clientlib.py
└── gaia/gaia_ceto/proto_mcp_http/server_config.json
```
