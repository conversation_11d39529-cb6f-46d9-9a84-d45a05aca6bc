DELETE FROM agbase_fundinground_investors WHERE fundinground_id IN (
 select id from agbase_fundinground 
where 
not
-- list various ways to be excluded from delete
 ( 
 (rec_inclusion in ('D','N') ) or
 (fr_code_id in(  select afrc1.id as afrc2_code from agbase_fundingroundcode afrc1
 left join agbase_fundingroundcode afrc2 on (afrc1.report_round_code_id=afrc2.id)  where afrc2.code='IGNORE') ) or
 (cb_uuid_fr is null)
 )
);



delete from agbase_fundinground 
where 
not
-- list various ways to be excluded from delete
( 
 (rec_inclusion in ('D','N') ) or
 (fr_code_id in(  select afrc1.id as afrc2_code from agbase_fundingroundcode afrc1
 left join agbase_fundingroundcode afrc2 on (afrc1.report_round_code_id=afrc2.id)  where afrc2.code='IGNORE') ) or
 (cb_uuid_fr is null)
)
;



