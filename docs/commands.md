- migration steps


# migrate commands
- note django schema used for all django-managed tables etc.
- first create schema using psql (or it doesnt work with no error):
```
ireport_20170430_b=# create schema django;
CREATE SCHEMA
```
- note: even running `python manage.py showmigrations` causes table django.django_migrations to be created
- `settings.py : DATABASES['default']['options'][options']='-c search_path=django,public'`
- its possible to wipe schema and start again:

```
python manage.py migrate agbase zero
python manage.py migrate admin zero
python manage.py migrate auth zero
python manage.py migrate contenttypes zero
python manage.py migrate sessions zero

# want to regen all migrations and rerun this?
# results in squashed migration
rm agbase/migrations/0*  
python manage.py makemigrations
python manage.py migrate
```



# manage commands

```
#python manage.py createsuperuser --username robin --email <EMAIL>
python manage.py shell

from django.contrib.auth.models import User; 
em='<EMAIL>'; 
User.objects.filter(email=em).delete(); 
User.objects.create_superuser('robin', em, 'Abcd1234!'); 
quit();

python manage.py loaddata agbase.AgtechCategory.json
python manage.py loaddata agbase.DataSource.json
python manage.py loaddata agbase.TagTree.json
python manage.py create_frcodes
```

### orgs

- psql:
```
psql -U postgres ireport_20170430_b

drop table if exists edits_orgs_merge_t;
select * into edits_orgs_merge_t from edits_orgs_merge;
alter table edits_orgs_merge_t add org_id serial;
alter table edits_orgs_merge_t rename column category to category_text;
alter table edits_orgs_merge_t add category_id integer;
alter table edits_orgs_merge_t add country_code_2 char(2);
```

```
python manage.py writefk_org_cat
python manage.py writefk_org_country
python manage.py copy_orgs_to_managed
````
- CONFIRMED up to this point :)


### rounds

- psql
```
# starting from ireport_20170430

    -- create af_fr_merged
drop view if exists af_fr_merged cascade;
create view af_fr_merged as
select
'2016FY'::text as src,
excl_report_fr as excl_report,
permalink,
company_name as name,
''::text as old_category,
''::text as short_description,
''::text as description,
''::text as website,
''::text as country,
''::text as country_code,
''::text as region,
''::text as state_code,
''::text as city,
annc_on,
cb_fr_usd,
cb_total_usd,
af_fr_code,
cb_fr_code,
stage_num as stage_curr,
af_fr_code as stage_lbl,
cb_fr_type,
''::text as investor_names,
cb_invrs_rnd,
fr_uuid,
org_uuid,
null::date as founded_at
from edits_gs_frorg_join
union 
select * from af_fr_pastfy_detail
;

drop table if exists af_fr_merged_table;
select * into af_fr_merged_table from af_fr_merged;
alter table af_fr_merged_table add fr_id serial;

python manage.py copy_fr_to_managed


--- historical
./scripts/100_agbase.sql  # enten; creates af_fr_allyears1, for 2014-2016 rounds
    # still need to import 2017 rounds

select * from edits_fr

select  permalink, rnd_src,annc_on,annc_on_q,annc_on_h,annc_on_y, af_fr_code, cb_fr_usd, cb_invrs_rnd, category, name, short_description, 
country_code,state_code,city into af_fr_allyears1_t from af_fr_allyears1 ;

alter table af_fr_allyears1_table add fr_id serial;
```

```
# not complete
# python manage.py copy_fr_to_managed
# python manage.py writefk_org_state

# not needed
#python manage.py annotate_tag_tree # use fixture instead?

```


### annotate_tag_tree
- purpose: write in more generated fields from tagtree to tagtree
- can avoid needing by load/restoring data

### writefk_org_cat
- good
- assumes there is 

### writefk_org_country
- good

### writefk_org_state
- may not work

### copy_orgs_to_managed
- run this

### copy_fr_to_managed
- not completed

